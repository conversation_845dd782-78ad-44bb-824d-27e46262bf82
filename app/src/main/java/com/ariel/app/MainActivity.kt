package com.ariel.app

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.navigation.NavController
import androidx.navigation.findNavController
import androidx.appcompat.app.AppCompatActivity
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.ActivityMainBinding
import com.ariel.app.ui.login.LoginActivity
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var sessionManager: SessionManager
    private lateinit var navController: NavController

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize session manager
        sessionManager = SessionManager(this)

        // Check if user is logged in, if not redirect to login
        if (!sessionManager.isLoggedIn()) {
            redirectToLogin()
            return
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.appBarMain.toolbar)

        // Hide the FAB in the main activity since we're using a FAB in the TicketsFragment
        binding.appBarMain.fab.visibility = View.GONE

        navController = findNavController(R.id.nav_host_fragment_content_main)

        // Set up back navigation
        setupBackNavigation()
    }



    /**
     * Sets up back navigation handling.
     */
    private fun setupBackNavigation() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // If we're at the home destination, close the app
                // Otherwise, navigate up
                if (navController.currentDestination?.id == R.id.nav_home) {
                    finish()
                } else {
                    navController.navigateUp()
                }
            }
        })
    }



    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)

        // Set up menu item visibility based on user role
        setupMenuVisibility(menu)

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_home -> {
                // Navigate to home
                navController.navigate(R.id.nav_home)
                true
            }
            R.id.action_search -> {
                // Show search dialog
                showSearchDialog()
                true
            }
            R.id.action_new_ticket -> {
                // Navigate to new ticket
                navController.navigate(R.id.nav_new_ticket)
                true
            }
            R.id.action_new_knowledge -> {
                // Navigate to new knowledge
                navController.navigate(R.id.nav_new_knowledge)
                true
            }
            R.id.action_new_faq -> {
                // Navigate to new FAQ
                navController.navigate(R.id.nav_new_faq)
                true
            }
            R.id.action_new_event -> {
                // Navigate to new event
                navController.navigate(R.id.nav_new_event)
                true
            }
            R.id.action_tickets -> {
                // Navigate to tickets
                navController.navigate(R.id.nav_tickets)
                true
            }
            R.id.action_knowledges -> {
                // Navigate to knowledges
                navController.navigate(R.id.nav_knowledges)
                true
            }
            R.id.action_faqs -> {
                // Navigate to FAQs
                navController.navigate(R.id.nav_faqs)
                true
            }
            R.id.action_events -> {
                // Navigate to events
                navController.navigate(R.id.nav_events)
                true
            }
            R.id.action_users -> {
                // Navigate to users
                navController.navigate(R.id.nav_users)
                true
            }
            R.id.action_profile -> {
                // Navigate to profile
                navController.navigate(R.id.nav_profile)
                true
            }
            R.id.action_logout -> {
                // Show confirmation dialog before logging out
                showLogoutConfirmationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * Shows a dialog for entering a search query.
     */
    private fun showSearchDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_search, null)
        val searchQueryEditText = dialogView.findViewById<EditText>(R.id.et_search_query)
        val searchButton = dialogView.findViewById<Button>(R.id.btn_search)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val progressBar = dialogView.findViewById<ProgressBar>(R.id.progress_bar)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        searchButton.setOnClickListener {
            val query = searchQueryEditText.text.toString().trim()
            if (query.isNotEmpty()) {
                // Navigate to search results fragment with the query
                val bundle = bundleOf("query" to query)
                navController.navigate(R.id.searchResultsFragment, bundle)
                dialog.dismiss()
            } else {
                // Show error if query is empty
                searchQueryEditText.error = getString(R.string.search_hint)
            }
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Shows a confirmation dialog before logging out.
     */
    private fun showLogoutConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_logout_confirm, null)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            // User confirmed logout
            sessionManager.clearSession()
            redirectToLogin()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp() || super.onSupportNavigateUp()
    }

    /**
     * Redirects to the LoginActivity and finishes the current activity.
     */
    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * Sets up menu item visibility based on user role.
     *
     * @param menu The options menu.
     */
    private fun setupMenuVisibility(menu: Menu) {
        val user = sessionManager.getUser()

        // Hide New Ticket menu item for superusers and limited admins
        val newTicketMenuItem = menu.findItem(R.id.action_new_ticket)
        if (user != null && (user.isSuperuser || user.isLimitedAdmin)) {
            newTicketMenuItem?.isVisible = false
        }

        // Show New Knowledge, New FAQ, and New Event menu items only for superusers
        val newKnowledgeMenuItem = menu.findItem(R.id.action_new_knowledge)
        val newFaqMenuItem = menu.findItem(R.id.action_new_faq)
        val newEventMenuItem = menu.findItem(R.id.action_new_event)

        if (user == null || !user.isSuperuser) {
            newKnowledgeMenuItem?.isVisible = false
            newFaqMenuItem?.isVisible = false
            newEventMenuItem?.isVisible = false
        }

        // Hide the entire add menu if no items are visible
        val addMenuItem = menu.findItem(R.id.action_add_menu)
        val hasVisibleItems = (newTicketMenuItem?.isVisible == true) ||
                             (newKnowledgeMenuItem?.isVisible == true) ||
                             (newFaqMenuItem?.isVisible == true) ||
                             (newEventMenuItem?.isVisible == true)

        addMenuItem?.isVisible = hasVisibleItems

        // Hide Users menu item for non-superusers
        val usersMenuItem = menu.findItem(R.id.action_users)
        if (user == null || !user.isSuperuser) {
            usersMenuItem?.isVisible = false
        }
    }
}
