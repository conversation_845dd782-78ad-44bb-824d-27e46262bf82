<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <item
        android:id="@+id/action_home"
        android:icon="@drawable/ic_menu_home"
        android:orderInCategory="80"
        android:title="@string/menu_home"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_search"
        android:icon="@drawable/ic_search"
        android:orderInCategory="90"
        android:title="@string/search"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_add_menu"
        android:icon="@drawable/ic_menu_new"
        android:orderInCategory="95"
        android:title="@string/new_ticket"
        app:showAsAction="ifRoom">
        <menu>
            <item
                android:id="@+id/action_new_ticket"
                android:title="@string/new_ticket" />
            <item
                android:id="@+id/action_new_knowledge"
                android:title="@string/new_knowledge" />
            <item
                android:id="@+id/action_new_faq"
                android:title="@string/new_faq" />
            <item
                android:id="@+id/action_new_event"
                android:title="@string/new_event" />
        </menu>
    </item>

    <!-- Navigation items in overflow menu -->
    <item
        android:id="@+id/action_tickets"
        android:orderInCategory="200"
        android:title="@string/menu_tickets"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_knowledges"
        android:orderInCategory="210"
        android:title="@string/menu_knowledges"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_faqs"
        android:orderInCategory="220"
        android:title="@string/menu_faqs"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_events"
        android:orderInCategory="230"
        android:title="@string/menu_events"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_users"
        android:orderInCategory="240"
        android:title="@string/menu_users"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_profile"
        android:orderInCategory="250"
        android:title="@string/menu_profile"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_logout"
        android:icon="@drawable/ic_logout"
        android:orderInCategory="300"
        android:title="@string/logout"
        app:showAsAction="ifRoom" />
</menu>
